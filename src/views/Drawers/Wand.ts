import { BaseDrawer, DrawPoint } from './BaseDrawer';
import Konva from 'konva';
import OpenSeadragon from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { isMouseLeftOrTouch } from '@/utils/index';

// ==================== 类型定义 ====================

export interface WandOptions {
  tolerance: number;
  minRadius?: number;
  maxRadius?: number;
  roughness?: number;
  wandType?: 'brightness' | 'rgb' | 'hsv' | 'lab';
  maxRegionSize?: number;
  searchRadius?: number;
  adaptiveTolerance?: boolean;
  smoothing?: boolean;
  multiThreaded?: boolean;
}

export interface Region {
  id: string;
  pixels: Set<number>;
  bounds: { minX: number; minY: number; maxX: number; maxY: number };
  contour: number[];
  area: number;
  centroid: { x: number; y: number };
  confidence: number;
}

export interface ColorValue {
  r?: number;
  g?: number;
  b?: number;
  h?: number;
  s?: number;
  v?: number;
  l?: number;
  a?: number;
  brightness?: number;
}

export interface ProcessingContext {
  imageData: ImageData;
  width: number;
  height: number;
  zoom: number;
  isDragMode: boolean;
  tolerance: number;
  searchRadius: number;
}

// ==================== 性能优化工具类 ====================

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startTimer(operation: string): () => void {
    const start = performance.now();
    return () => {
      const duration = performance.now() - start;
      if (!this.metrics.has(operation)) {
        this.metrics.set(operation, []);
      }
      this.metrics.get(operation)!.push(duration);

      // 保持最近100次记录
      const records = this.metrics.get(operation)!;
      if (records.length > 100) {
        records.splice(0, records.length - 100);
      }
    };
  }

  getAverageTime(operation: string): number {
    const records = this.metrics.get(operation);
    if (!records || records.length === 0) return 0;
    return records.reduce((sum, time) => sum + time, 0) / records.length;
  }

  shouldOptimize(operation: string, threshold: number = 16): boolean {
    return this.getAverageTime(operation) > threshold;
  }
}

class MemoryPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (item: T) => void;

  constructor(createFn: () => T, resetFn: (item: T) => void, initialSize: number = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;

    // 预分配对象
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(createFn());
    }
  }

  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }

  release(item: T): void {
    this.resetFn(item);
    if (this.pool.length < 50) {
      // 限制池大小
      this.pool.push(item);
    }
  }
}

// ==================== 智能算法引擎 ====================

class SmartWandEngine {
  private static instance: SmartWandEngine;
  private performanceMonitor = PerformanceMonitor.getInstance();

  // 对象池
  private regionPool = new MemoryPool<Set<number>>(
    () => new Set<number>(),
    (set) => set.clear()
  );

  private pointPool = new MemoryPool<{ x: number; y: number }>(
    () => ({ x: 0, y: 0 }),
    (point) => {
      point.x = 0;
      point.y = 0;
    }
  );

  static getInstance(): SmartWandEngine {
    if (!SmartWandEngine.instance) {
      SmartWandEngine.instance = new SmartWandEngine();
    }
    return SmartWandEngine.instance;
  }

  // 智能容差计算
  calculateAdaptiveTolerance(
    baseColor: ColorValue,
    context: ProcessingContext,
    regionCount: number
  ): number {
    const endTimer = this.performanceMonitor.startTimer('adaptiveTolerance');

    let tolerance = context.tolerance;

    // 基于图像特征调整
    if (context.isDragMode) {
      tolerance *= 1.1; // 拖动时稍微放宽
    }

    // 基于已检测区域数量调整
    if (regionCount === 0) {
      tolerance *= 1.2; // 首次检测时放宽
    } else if (regionCount > 5) {
      tolerance *= 0.9; // 已有多个区域时收紧
    }

    // 基于缩放级别调整
    const zoomFactor = Math.log2(context.zoom + 1);
    tolerance *= 1 + zoomFactor * 0.1;

    endTimer();
    return Math.max(5, Math.min(100, tolerance));
  }

  // 高性能颜色距离计算
  calculateColorDistance(color1: ColorValue, color2: ColorValue, wandType: string): number {
    switch (wandType) {
      case 'brightness':
        return Math.abs((color1.brightness || 0) - (color2.brightness || 0));

      case 'rgb':
        const dr = (color1.r || 0) - (color2.r || 0);
        const dg = (color1.g || 0) - (color2.g || 0);
        const db = (color1.b || 0) - (color2.b || 0);
        return Math.sqrt(dr * dr + dg * dg + db * db);

      case 'hsv':
        const dh = Math.min(
          Math.abs((color1.h || 0) - (color2.h || 0)),
          360 - Math.abs((color1.h || 0) - (color2.h || 0))
        );
        const ds = (color1.s || 0) - (color2.s || 0);
        const dv = (color1.v || 0) - (color2.v || 0);
        return Math.sqrt(dh * dh + ds * ds + dv * dv);

      default:
        return Math.abs((color1.brightness || 0) - (color2.brightness || 0));
    }
  }

  // 智能搜索半径计算
  calculateSearchRadius(context: ProcessingContext, regionCount: number): number {
    const baseRadius = Math.min(context.width, context.height) * 0.15;

    if (context.isDragMode) {
      // 拖动模式下根据缩放和已有区域调整
      const zoomFactor = Math.sqrt(context.zoom);
      const regionFactor = Math.max(0.5, 1 - regionCount * 0.1);
      return baseRadius * zoomFactor * regionFactor;
    }

    // 初始点击时使用更大的搜索半径
    return baseRadius * 2;
  }

  // 释放资源
  releaseResources(regions: Set<number>[]): void {
    regions.forEach((region) => this.regionPool.release(region));
  }
}

// ==================== 高性能魔棒绘制器 ====================

export class WandDrawer extends BaseDrawer<Konva.Group> {
  private _wandConfig: any;
  protected _ratio: any = { unit: 'px', scales: 1 };

  // 核心引擎
  private smartEngine = SmartWandEngine.getInstance();
  private performanceMonitor = PerformanceMonitor.getInstance();

  // 图像数据缓存
  private imageDataCache: {
    data: ImageData | null;
    timestamp: number;
    width: number;
    height: number;
  } = { data: null, timestamp: 0, width: 0, height: 0 };

  // 智能参数配置
  private wandConfig = {
    tolerance: 25,
    wandType: 'brightness' as 'brightness' | 'rgb' | 'hsv' | 'lab',
    maxRegionSize: 10000,
    searchRadius: 200,
    adaptiveTolerance: true,
    smoothing: true,
    minMoveDistance: 2,
    maxProcessingTime: 16, // 16ms目标，保持60fps
  };

  // 状态管理
  private hasSelection = false;
  private isDragging = false;
  private isProcessing = false;
  private lastProcessedPoint: { x: number; y: number } | null = null;
  private processingStartTime = 0;

  // 高性能数据结构
  private regions = new Map<string, Region>();
  private regionShapes = new Map<string, Konva.Shape>();
  private spatialIndex = new Map<string, Set<string>>(); // 空间索引，加速查找
  private visitedPixels = new Set<number>();
  private processedAreas = new Set<string>();
  private dragPath: DrawPoint[] = [];

  // 兼容性属性（保持与原代码的兼容性）
  private detectedRegions: Region[] = [];
  private imageData: ImageData | null = null;
  private canvasWidth = 0;
  private canvasHeight = 0;
  private tolerance = 25;
  private wandType: 'brightness' | 'rgb' | 'hsv' | 'lab' = 'brightness';
  private maxRegionSize = 10000;
  private searchRadius = 200;
  private minMoveDistance = 2;
  private selectionMask: Uint8Array | null = null;

  // 防抖和节流
  private processingQueue: Array<() => void> = [];
  private isProcessingQueue = false;
  private frameId: number | null = null;

  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, wandConfig: any) {
    super(layer, viewer, wandConfig);
    this._wandConfig = wandConfig;
    this.startPoint = {};
  }

  override start() {
    super.start();
    this.viewer.setMouseNavEnabled(false);
    this.layer.getStage().container().style.cursor = 'crosshair';
    const canvasDiv = this.layer.getStage().container();
    if (canvasDiv) {
      canvasDiv.style.zIndex = '2';
    }
  }

  override end() {
    try {
      this.isDragging = false;

      // 安全恢复鼠标导航
      if (this.viewer && typeof this.viewer.setMouseNavEnabled === 'function') {
        this.viewer.setMouseNavEnabled(true);
      }

      // 安全恢复光标样式
      const stage = this.layer?.getStage();
      if (stage && stage.container()) {
        const container = stage.container();
        if (container && container.style) {
          container.style.cursor = 'default';
          container.style.zIndex = '1';
        }
      }

      // 创建标记并调用回调
      this.createMarkerAndCallback();

      super.end();
    } catch (error) {
      console.error('结束绘制时出错:', error);
      // 确保基本清理
      this.isDragging = false;
      try {
        super.end();
      } catch (superError) {
        console.error('调用super.end()时出错:', superError);
      }
    }
  }

  override drawDown() {
    if (!this.node) {
      this.start();

      // 使用标准的坐标获取方式（与其他工具保持一致）
      this.startPoint = this.curPoint;
      this.isDragging = true;
      this.dragPath = [this.startPoint];
      this.lastProcessedPoint = { x: this.startPoint.x, y: this.startPoint.y };

      // 创建节点
      this.node = this.createNode();
      this.layer.add(this.node);
      this.setNodeId();

      // 立即获取图像数据并执行初始选择
      this.initializeWandSelectionSync();
    }
  }

  /**
   * 初始化魔杖选择 - 同步版本，提高响应性
   */
  private initializeWandSelectionSync() {
    try {
      // 立即获取图像数据
      this.getImageDataFromViewerSync();

      // 检查修饰键
      const addToSelection = this._wandConfig.ev?.shiftKey;
      const subtractFromSelection = this._wandConfig.ev?.altKey;

      // 清除之前的选择（如果不是添加模式）
      if (!addToSelection && !subtractFromSelection) {
        this.clearSelection();
        this.visitedPixels.clear();
        this.processedAreas.clear();
      }

      // 执行初始选择
      const hasInitialRegion = this.performWandSelectionSync(
        this.startPoint.imageX!,
        this.startPoint.imageY!,
        addToSelection,
        subtractFromSelection
      );

      // 如果初始选择成功，标记起始区域为已处理
      if (hasInitialRegion) {
        const gridSize = 6; // 使用与drawMove相同的网格大小
        const gridX = Math.floor(this.startPoint.imageX! / gridSize);
        const gridY = Math.floor(this.startPoint.imageY! / gridSize);
        const areaKey = `${gridX}_${gridY}`;
        this.processedAreas.add(areaKey);
      }
    } catch (error) {
      console.error('魔杖初始化失败:', error);
    }
  }

  override drawMove() {
    try {
      if (!this.isDragging || !this.imageData) return;

      // 使用标准的坐标获取方式（与其他工具保持一致）
      const currentPoint = this.curPoint;

      // 验证当前点的有效性
      if (
        !currentPoint ||
        !isFinite(currentPoint.x) ||
        !isFinite(currentPoint.y) ||
        !isFinite(currentPoint.imageX!) ||
        !isFinite(currentPoint.imageY!)
      ) {
        console.warn('无效的当前点坐标');
        return;
      }

      // 检查移动距离 - 智能距离检查
      if (this.lastProcessedPoint) {
        const distance = Math.sqrt(
          Math.pow(currentPoint.x - this.lastProcessedPoint.x, 2) +
            Math.pow(currentPoint.y - this.lastProcessedPoint.y, 2)
        );

        if (distance < this.minMoveDistance) {
          return; // 移动距离太小，跳过处理
        }
      }

      // 智能网格大小：根据缩放级别调整
      const zoom = this.viewer.viewport.getZoom();
      const baseGridSize = 6;
      const gridSize = Math.max(2, Math.min(12, baseGridSize / Math.sqrt(zoom)));

      const gridX = Math.floor(currentPoint.imageX! / gridSize);
      const gridY = Math.floor(currentPoint.imageY! / gridSize);
      const areaKey = `${gridX}_${gridY}`;

      if (this.processedAreas.has(areaKey)) {
        return; // 已处理过这个区域，跳过
      }

      // 限制拖动路径长度，防止内存过度使用
      this.dragPath.push(currentPoint);
      if (this.dragPath.length > 1000) {
        this.dragPath = this.dragPath.slice(-500); // 保留最近的500个点
      }

      // 立即执行拖动选择
      this.handleDragSelectionSync(currentPoint, areaKey);

      // 更新最后处理的点
      this.lastProcessedPoint = { x: currentPoint.x, y: currentPoint.y };

      // 定期清理处理区域缓存，防止内存泄漏
      if (this.processedAreas.size > 2000) {
        const keysToDelete = Array.from(this.processedAreas).slice(0, 1000);
        keysToDelete.forEach((key) => this.processedAreas.delete(key));
      }
    } catch (error) {
      console.error('drawMove错误:', error);
    }
  }

  /**
   * 处理拖动选择 - 同步版本，提高响应性
   */
  private handleDragSelectionSync(currentPoint: any, areaKey: string) {
    try {
      // 检查修饰键
      const addToSelection = this._wandConfig.ev?.shiftKey;
      const subtractFromSelection = this._wandConfig.ev?.altKey;

      // 立即执行拖动选择
      this.performWandSelectionSync(
        currentPoint.imageX!,
        currentPoint.imageY!,
        addToSelection || this.hasSelection,
        subtractFromSelection,
        areaKey
      );
    } catch (error) {
      console.error('拖动选择失败:', error);
    }
  }

  override drawUp() {
    if (isMouseLeftOrTouch(this._wandConfig.ev)) {
      this.isDragging = false;
      this.status = DrawStatus.drawingCompleted;
      this.end();
    }
  }

  // 创建节点（魔棒工具创建的是一个组，包含多个区域形状）
  createNode() {
    const group = new Konva.Group({
      name: DrawType.wand,
      strokeScaleEnabled: false,
      zoom: this.viewer.viewport.getZoom(),
    });
    return group;
  }

  /**
   * 设置魔棒参数
   */
  setWandOptions(options: WandOptions) {
    if (options.tolerance !== undefined) this.tolerance = options.tolerance;
    if (options.wandType !== undefined) this.wandType = options.wandType;
    if (options.maxRegionSize !== undefined) this.maxRegionSize = options.maxRegionSize;
    if (options.searchRadius !== undefined) this.searchRadius = options.searchRadius;
  }

  /**
   * 从viewer获取图像数据 - 同步版本
   */
  private getImageDataFromViewerSync(): ImageData {
    const canvas = this.viewer.drawer.canvas as HTMLCanvasElement;
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Cannot get 2D context');

    this.canvasWidth = canvas.width;
    this.canvasHeight = canvas.height;
    this.imageData = ctx.getImageData(0, 0, this.canvasWidth, this.canvasHeight);

    // 初始化选择掩码
    if (!this.selectionMask) {
      this.selectionMask = new Uint8Array(this.canvasWidth * this.canvasHeight);
    }

    return this.imageData;
  }

  /**
   * 检测独立区域（同步版本，增加安全检查）
   */
  private detectRegionSync(startX: number, startY: number): Region | null {
    try {
      if (!this.imageData) return null;

      const width = this.canvasWidth;
      const height = this.canvasHeight;
      const data = this.imageData.data;

      // 确保坐标在范围内
      startX = Math.max(0, Math.min(startX, width - 1));
      startY = Math.max(0, Math.min(startY, height - 1));

      // 检查起始点是否有效
      if (startX < 0 || startX >= width || startY < 0 || startY >= height) {
        return null;
      }

      // 智能搜索半径：根据图像大小和当前模式调整
      const isDragMode = this.isDragging && this.dragPath.length > 1;
      const baseRadius = Math.min(width, height) * 0.1; // 图像大小的10%
      const searchRadius = isDragMode ? Math.min(this.searchRadius, baseRadius) : baseRadius * 2;

      const searchMinX = Math.max(0, startX - searchRadius);
      const searchMaxX = Math.min(width - 1, startX + searchRadius);
      const searchMinY = Math.max(0, startY - searchRadius);
      const searchMaxY = Math.min(height - 1, startY + searchRadius);

      // 获取起始点的颜色值
      const startIndex = (startY * width + startX) * 4;
      if (startIndex >= data.length) return null;

      const startColor = this.getPixelValue(data, startIndex);

      // 使用洪水填充算法检测连通区域，增加安全检查
      const visited = new Uint8Array(width * height);
      const queue = [{ x: startX, y: startY }];
      const regionPixels = new Set<number>();

      let minX = startX,
        maxX = startX;
      let minY = startY,
        maxY = startY;
      let iterations = 0;
      const maxIterations = this.maxRegionSize * 2; // 防止无限循环

      while (
        queue.length > 0 &&
        regionPixels.size < this.maxRegionSize &&
        iterations < maxIterations
      ) {
        iterations++;
        const current = queue.shift();
        if (!current) break;

        const { x, y } = current;

        // 边界检查
        if (x < 0 || x >= width || y < 0 || y >= height) continue;

        const index = y * width + x;
        if (index < 0 || index >= visited.length || visited[index]) continue;

        // 搜索范围限制（只在拖动模式下应用）
        if (isDragMode && (x < searchMinX || x > searchMaxX || y < searchMinY || y > searchMaxY)) {
          continue;
        }

        visited[index] = 1;

        // 获取当前像素的颜色值
        const currentIndex = (y * width + x) * 4;
        if (currentIndex >= data.length) continue;

        const currentColor = this.getPixelValue(data, currentIndex);

        // 检查颜色相似性
        if (this.isColorSimilar(startColor, currentColor)) {
          regionPixels.add(index);

          // 更新边界
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y);

          // 添加4邻域像素到队列（减少8邻域为4邻域，提高性能）
          if (regionPixels.size < this.maxRegionSize && queue.length < 10000) {
            // 限制队列大小
            const neighbors = [
              { x: x + 1, y: y },
              { x: x - 1, y: y },
              { x: x, y: y + 1 },
              { x: x, y: y - 1 },
            ];

            for (const neighbor of neighbors) {
              const neighborIndex = neighbor.y * width + neighbor.x;
              if (
                neighbor.x >= 0 &&
                neighbor.x < width &&
                neighbor.y >= 0 &&
                neighbor.y < height &&
                neighborIndex >= 0 &&
                neighborIndex < visited.length &&
                !visited[neighborIndex]
              ) {
                queue.push(neighbor);
              }
            }
          }
        }
      }

      // 检查是否检测到有效区域
      if (regionPixels.size < 3) return null; // 至少需要3个像素

      // 提取轮廓
      const contour = this.extractContour(regionPixels, { minX, minY, maxX, maxY });

      // 计算质心
      let centroidX = 0,
        centroidY = 0;
      regionPixels.forEach((pixel) => {
        const x = pixel % width;
        const y = Math.floor(pixel / width);
        centroidX += x;
        centroidY += y;
      });
      centroidX /= regionPixels.size;
      centroidY /= regionPixels.size;

      // 计算置信度（基于区域大小和形状规整度）
      const area = regionPixels.size;
      const boundingArea = (maxX - minX + 1) * (maxY - minY + 1);
      const confidence = Math.min(1, (area / boundingArea) * 0.8 + 0.2);

      return {
        id: `region_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        pixels: regionPixels,
        bounds: { minX, minY, maxX, maxY },
        contour: contour,
        area: area,
        centroid: { x: centroidX, y: centroidY },
        confidence: confidence,
      };
    } catch (error) {
      console.error('区域检测错误:', error);
      return null;
    }
  }

  /**
   * 获取像素值（根据wandType）- 从WandEditor.ts复制
   */
  private getPixelValue(data: Uint8ClampedArray, index: number): any {
    const r = data[index];
    const g = data[index + 1];
    const b = data[index + 2];

    switch (this.wandType) {
      case 'brightness':
        return 0.299 * r + 0.587 * g + 0.114 * b;
      case 'rgb':
        return { r, g, b };
      case 'hsv':
        return this.rgbToHsv(r, g, b);
      case 'lab':
        return this.rgbToLab(r, g, b);
      default:
        return 0.299 * r + 0.587 * g + 0.114 * b;
    }
  }

  /**
   * 检查颜色相似性 - 智能容差调整
   */
  private isColorSimilar(color1: any, color2: any): boolean {
    const distance = this.colorDistance(color1, color2);

    // 智能容差：根据拖动模式和区域大小动态调整
    let adjustedTolerance = this.tolerance;

    if (this.isDragging && this.dragPath.length > 1) {
      // 拖动模式下稍微放宽容差，提高连续性
      adjustedTolerance = this.tolerance * 1.2;
    }

    // 根据当前检测到的区域数量调整容差
    if (this.detectedRegions.length === 0) {
      // 如果还没有检测到任何区域，稍微放宽容差
      adjustedTolerance = this.tolerance * 1.3;
    }

    return distance <= adjustedTolerance;
  }

  /**
   * 计算颜色距离 - 从WandEditor.ts复制
   */
  private colorDistance(color1: any, color2: any): number {
    switch (this.wandType) {
      case 'brightness':
        return Math.abs(color1 - color2);
      case 'rgb':
        return Math.sqrt(
          Math.pow(color1.r - color2.r, 2) +
            Math.pow(color1.g - color2.g, 2) +
            Math.pow(color1.b - color2.b, 2)
        );
      case 'hsv':
        return Math.sqrt(
          Math.pow(color1.h - color2.h, 2) +
            Math.pow(color1.s - color2.s, 2) +
            Math.pow(color1.v - color2.v, 2)
        );
      case 'lab':
        return Math.sqrt(
          Math.pow(color1.l - color2.l, 2) +
            Math.pow(color1.a - color2.a, 2) +
            Math.pow(color1.b - color2.b, 2)
        );
      default:
        return Math.abs(color1 - color2);
    }
  }

  /**
   * RGB转HSV - 从WandEditor.ts复制
   */
  private rgbToHsv(r: number, g: number, b: number): { h: number; s: number; v: number } {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const diff = max - min;

    let h = 0;
    if (diff !== 0) {
      if (max === r) h = ((g - b) / diff) % 6;
      else if (max === g) h = (b - r) / diff + 2;
      else h = (r - g) / diff + 4;
    }
    h = Math.round(h * 60);
    if (h < 0) h += 360;

    const s = max === 0 ? 0 : diff / max;
    const v = max;

    return { h, s: s * 100, v: v * 100 };
  }

  /**
   * RGB转LAB - 从WandEditor.ts复制
   */
  private rgbToLab(r: number, g: number, b: number): { l: number; a: number; b: number } {
    // RGB to XYZ
    r = r / 255;
    g = g / 255;
    b = b / 255;

    r = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;
    g = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;
    b = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;

    let x = (r * 0.4124 + g * 0.3576 + b * 0.1805) / 0.95047;
    let y = (r * 0.2126 + g * 0.7152 + b * 0.0722) / 1.0;
    let z = (r * 0.0193 + g * 0.1192 + b * 0.9505) / 1.08883;

    x = x > 0.008856 ? Math.pow(x, 1 / 3) : 7.787 * x + 16 / 116;
    y = y > 0.008856 ? Math.pow(y, 1 / 3) : 7.787 * y + 16 / 116;
    z = z > 0.008856 ? Math.pow(z, 1 / 3) : 7.787 * z + 16 / 116;

    const l = 116 * y - 16;
    const a = 500 * (x - y);
    const bLab = 200 * (y - z);

    return { l, a: a, b: bLab };
  }

  /**
   * 提取区域轮廓 - 从WandEditor.ts完整复制
   */
  private extractContour(
    pixels: Set<number>,
    bounds: { minX: number; minY: number; maxX: number; maxY: number }
  ): number[] {
    const width = this.canvasWidth;
    const contourPoints: Array<[number, number]> = [];

    // 创建局部二值图像
    const localWidth = bounds.maxX - bounds.minX + 3;
    const localHeight = bounds.maxY - bounds.minY + 3;
    const binaryImage = new Uint8Array(localWidth * localHeight);

    // 填充二值图像
    pixels.forEach((idx) => {
      const x = idx % width;
      const y = Math.floor(idx / width);
      const localX = x - bounds.minX + 1;
      const localY = y - bounds.minY + 1;
      const localIdx = localY * localWidth + localX;
      binaryImage[localIdx] = 1;
    });

    // 使用Moore邻域追踪算法提取轮廓
    const startX = 1;
    const startY = 1;

    // 找到第一个边界点
    let found = false;
    let currentX = startX;
    let currentY = startY;

    for (let y = 0; y < localHeight && !found; y++) {
      for (let x = 0; x < localWidth && !found; x++) {
        const idx = y * localWidth + x;
        if (binaryImage[idx] === 1) {
          // 检查是否是边界点
          const hasEmptyNeighbor =
            (x > 0 && binaryImage[idx - 1] === 0) ||
            (x < localWidth - 1 && binaryImage[idx + 1] === 0) ||
            (y > 0 && binaryImage[idx - localWidth] === 0) ||
            (y < localHeight - 1 && binaryImage[idx + localWidth] === 0);

          if (hasEmptyNeighbor) {
            currentX = x;
            currentY = y;
            found = true;
          }
        }
      }
    }

    if (!found) return [];

    // Moore邻域追踪
    const startIdx = currentY * localWidth + currentX;
    const directions = [
      [-1, -1],
      [0, -1],
      [1, -1],
      [1, 0],
      [1, 1],
      [0, 1],
      [-1, 1],
      [-1, 0],
    ];

    let direction = 0;
    const maxSteps = pixels.size * 2;
    let steps = 0;

    do {
      // 添加当前点到轮廓
      const worldX = currentX + bounds.minX - 1;
      const worldY = currentY + bounds.minY - 1;
      contourPoints.push([worldX, worldY]);

      // 寻找下一个轮廓点
      let foundNext = false;
      for (let i = 0; i < 8; i++) {
        const nextDir = (direction + 6 + i) % 8;
        const [dx, dy] = directions[nextDir];
        const nextX = currentX + dx;
        const nextY = currentY + dy;

        if (nextX >= 0 && nextX < localWidth && nextY >= 0 && nextY < localHeight) {
          const nextIdx = nextY * localWidth + nextX;
          if (binaryImage[nextIdx] === 1) {
            currentX = nextX;
            currentY = nextY;
            direction = nextDir;
            foundNext = true;
            break;
          }
        }
      }

      if (!foundNext) break;
      steps++;
    } while (currentY * localWidth + currentX !== startIdx && steps < maxSteps);

    // 简化轮廓
    const simplified = this.simplifyContour(contourPoints, 1.5);

    // 转换为Konva坐标
    const konvaPoints: number[] = [];
    simplified.forEach(([x, y]: [number, number]) => {
      const konvaPoint = this._konvaPointFromPixl(x, y);
      konvaPoints.push(konvaPoint.x, konvaPoint.y);
    });

    return konvaPoints;
  }

  /**
   * 简化轮廓（Douglas-Peucker算法）- 从WandEditor.ts复制
   */
  private simplifyContour(
    points: Array<[number, number]>,
    epsilon: number
  ): Array<[number, number]> {
    if (points.length < 3) return points;

    // 找到距离最远的点
    let maxDist = 0;
    let maxIdx = 0;
    const start = points[0];
    const end = points[points.length - 1];

    for (let i = 1; i < points.length - 1; i++) {
      const dist = this.pointLineDistance(points[i], start, end);
      if (dist > maxDist) {
        maxDist = dist;
        maxIdx = i;
      }
    }

    // 如果最大距离大于阈值，递归简化
    if (maxDist > epsilon) {
      const left = this.simplifyContour(points.slice(0, maxIdx + 1), epsilon);
      const right = this.simplifyContour(points.slice(maxIdx), epsilon);
      return [...left.slice(0, -1), ...right];
    } else {
      return [start, end];
    }
  }

  /**
   * 计算点到线的距离 - 从WandEditor.ts复制
   */
  private pointLineDistance(
    point: [number, number],
    lineStart: [number, number],
    lineEnd: [number, number]
  ): number {
    const [x, y] = point;
    const [x1, y1] = lineStart;
    const [x2, y2] = lineEnd;

    const A = y2 - y1;
    const B = x1 - x2;
    const C = x2 * y1 - x1 * y2;

    return Math.abs(A * x + B * y + C) / Math.sqrt(A * A + B * B);
  }

  /**
   * 清除选择 - 高性能版本
   */
  private clearSelection() {
    try {
      // 清除新的Map结构
      this.regionShapes.forEach((shape) => {
        try {
          if (shape && typeof shape.destroy === 'function') {
            shape.destroy();
          }
        } catch (error) {
          console.warn('清除形状时出错:', error);
        }
      });

      // 重置所有数据结构
      this.regions.clear();
      this.regionShapes.clear();
      this.spatialIndex.clear();
      this.detectedRegions = [];
      this.visitedPixels.clear();
      this.processedAreas.clear();
      this.lastProcessedPoint = null;
      this.dragPath = [];

      if (this.selectionMask) {
        this.selectionMask.fill(0);
        this.hasSelection = false;
      }

      // 安全重绘
      if (this.layer && typeof this.layer.draw === 'function') {
        this.layer.draw();
      }
    } catch (error) {
      console.error('清除选择时出错:', error);
    }
  }

  /**
   * 执行魔棒选择（支持拖动模式）- 同步版本，提高响应性
   */
  private performWandSelectionSync(
    imageX: number,
    imageY: number,
    _addToSelection = false,
    subtractFromSelection = false,
    areaKey?: string
  ): boolean {
    try {
      if (!this.imageData) {
        console.warn('没有图像数据，跳过选择');
        return false;
      }

      // 验证输入坐标
      if (!isFinite(imageX) || !isFinite(imageY) || imageX < 0 || imageY < 0) {
        console.warn('无效的图像坐标:', imageX, imageY);
        return false;
      }

      // 将图像坐标转换为画布坐标
      const pt = new OpenSeadragon.Point(Number(imageX), Number(imageY));
      const viewportPoint = this.viewer.viewport.imageToViewportCoordinates(pt.x, pt.y);
      const pixelPoint = this.viewer.viewport.pixelFromPoint(viewportPoint);

      const x = Math.round(pixelPoint.x);
      const y = Math.round(pixelPoint.y);

      // 验证转换后的坐标
      if (
        !isFinite(x) ||
        !isFinite(y) ||
        x < 0 ||
        y < 0 ||
        x >= this.canvasWidth ||
        y >= this.canvasHeight
      ) {
        console.warn('转换后的坐标超出范围:', x, y);
        return false;
      }

      // 检测区域
      const region = this.detectRegionSync(x, y);

      if (region && region.pixels.size >= 3) {
        // 至少3个像素
        // 检查是否有足够的新像素（避免重复处理相同区域）
        const newPixels = new Set<number>();
        let newPixelCount = 0;

        // 优化：不创建完整的新像素集合，只计数
        for (const pixel of region.pixels) {
          if (!this.visitedPixels.has(pixel)) {
            newPixelCount++;
            if (newPixelCount <= 100) {
              // 限制检查数量，提高性能
              newPixels.add(pixel);
            }
          }
        }

        // 智能阈值：根据区域大小调整要求
        const minNewPixels = Math.max(1, Math.min(5, region.pixels.size * 0.1));

        if (newPixelCount >= minNewPixels) {
          // 限制访问像素的数量，防止内存过度使用
          if (this.visitedPixels.size > 50000) {
            this.visitedPixels.clear(); // 清理旧的访问记录
          }

          // 将新像素标记为已访问
          newPixels.forEach((pixel: number) => this.visitedPixels.add(pixel));

          if (subtractFromSelection) {
            this.subtractRegionFromSelection(region);
          } else {
            this.addRegionToSelection(region);
          }

          // 标记区域为已处理
          if (areaKey) {
            this.processedAreas.add(areaKey);
          }

          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('魔棒选择错误:', error);
      return false;
    }
  }

  /**
   * 添加区域到选择 - 高性能版本
   */
  private addRegionToSelection(region: Region) {
    const endTimer = this.performanceMonitor.startTimer('addRegionToSelection');

    try {
      // 使用空间索引快速查找重叠区域
      const overlappingRegionIds = this.findOverlappingRegions(region);

      if (overlappingRegionIds.length > 0) {
        // 合并重叠的区域
        const regionsToMerge = [
          region,
          ...overlappingRegionIds.map((id) => this.regions.get(id)!).filter(Boolean),
        ];
        const mergedRegion = this.mergeRegions(regionsToMerge);

        // 移除旧的重叠区域
        overlappingRegionIds.forEach((id) => {
          const shape = this.regionShapes.get(id);
          if (shape) {
            shape.destroy();
            this.regionShapes.delete(id);
          }
          this.regions.delete(id);
          this.removeFromSpatialIndex(id);
        });

        // 添加合并后的区域
        this.regions.set(mergedRegion.id, mergedRegion);
        this.detectedRegions = Array.from(this.regions.values()); // 同步数组
        this.createRegionShape(mergedRegion);
        this.addToSpatialIndex(mergedRegion);
      } else {
        // 没有重叠，直接添加
        this.regions.set(region.id, region);
        this.detectedRegions.push(region);
        this.createRegionShape(region);
        this.addToSpatialIndex(region);
      }

      this.hasSelection = true;
    } catch (error) {
      console.error('添加区域时出错:', error);
    } finally {
      endTimer();
    }
  }

  /**
   * 空间索引相关方法
   */
  private addToSpatialIndex(region: Region) {
    const gridSize = 50; // 空间网格大小
    const { minX, minY, maxX, maxY } = region.bounds;

    for (let x = Math.floor(minX / gridSize); x <= Math.floor(maxX / gridSize); x++) {
      for (let y = Math.floor(minY / gridSize); y <= Math.floor(maxY / gridSize); y++) {
        const key = `${x}_${y}`;
        if (!this.spatialIndex.has(key)) {
          this.spatialIndex.set(key, new Set());
        }
        this.spatialIndex.get(key)!.add(region.id);
      }
    }
  }

  private removeFromSpatialIndex(regionId: string) {
    this.spatialIndex.forEach((regionSet) => {
      regionSet.delete(regionId);
    });
  }

  private findOverlappingRegions(region: Region): string[] {
    const gridSize = 50;
    const { minX, minY, maxX, maxY } = region.bounds;
    const candidateIds = new Set<string>();

    // 收集候选区域
    for (let x = Math.floor(minX / gridSize); x <= Math.floor(maxX / gridSize); x++) {
      for (let y = Math.floor(minY / gridSize); y <= Math.floor(maxY / gridSize); y++) {
        const key = `${x}_${y}`;
        const regionSet = this.spatialIndex.get(key);
        if (regionSet) {
          regionSet.forEach((id) => candidateIds.add(id));
        }
      }
    }

    // 精确检查重叠
    const overlapping: string[] = [];
    candidateIds.forEach((id) => {
      const existingRegion = this.regions.get(id);
      if (existingRegion && this.regionsOverlap(existingRegion, region)) {
        overlapping.push(id);
      }
    });

    return overlapping;
  }

  /**
   * 从选择中减去区域
   */
  private subtractRegionFromSelection(region: Region) {
    // 简化实现：移除重叠的区域
    const newRegions = this.detectedRegions.filter((existingRegion) => {
      return !this.regionsOverlap(existingRegion, region);
    });

    if (newRegions.length !== this.detectedRegions.length) {
      this.detectedRegions = newRegions;
      this.redrawAllRegions();
    }
  }

  /**
   * 检查两个区域是否重叠
   */
  private regionsOverlap(region1: Region, region2: Region): boolean {
    // 检查边界框是否重叠
    if (
      region1.bounds.maxX < region2.bounds.minX ||
      region2.bounds.maxX < region1.bounds.minX ||
      region1.bounds.maxY < region2.bounds.minY ||
      region2.bounds.maxY < region1.bounds.minY
    ) {
      return false;
    }

    // 检查像素级重叠
    for (const pixel of region1.pixels) {
      if (region2.pixels.has(pixel)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 合并多个区域 - 高性能版本
   */
  private mergeRegions(regions: Region[]): Region {
    const mergedPixels = new Set<number>();
    let minX = Infinity,
      maxX = -Infinity;
    let minY = Infinity,
      maxY = -Infinity;
    let totalArea = 0;
    let weightedCentroidX = 0,
      weightedCentroidY = 0;
    let totalConfidence = 0;

    for (const region of regions) {
      region.pixels.forEach((pixel) => mergedPixels.add(pixel));
      minX = Math.min(minX, region.bounds.minX);
      maxX = Math.max(maxX, region.bounds.maxX);
      minY = Math.min(minY, region.bounds.minY);
      maxY = Math.max(maxY, region.bounds.maxY);

      // 加权质心计算
      totalArea += region.area;
      weightedCentroidX += region.centroid.x * region.area;
      weightedCentroidY += region.centroid.y * region.area;
      totalConfidence += region.confidence * region.area;
    }

    const contour = this.extractContour(mergedPixels, { minX, minY, maxX, maxY });

    return {
      id: `merged_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      pixels: mergedPixels,
      bounds: { minX, minY, maxX, maxY },
      contour,
      area: mergedPixels.size,
      centroid: {
        x: weightedCentroidX / totalArea,
        y: weightedCentroidY / totalArea,
      },
      confidence: totalConfidence / totalArea,
    };
  }

  /**
   * 重绘所有区域 - 高性能版本
   */
  private redrawAllRegions() {
    const endTimer = this.performanceMonitor.startTimer('redrawAllRegions');

    try {
      // 清除现有形状
      this.regionShapes.forEach((shape) => shape.destroy());
      this.regionShapes.clear();

      // 重新创建所有区域形状
      this.detectedRegions.forEach((region) => {
        this.createRegionShape(region);
      });

      this.layer.draw();
    } catch (error) {
      console.error('重绘区域时出错:', error);
    } finally {
      endTimer();
    }
  }

  /**
   * 创建区域形状 - 高性能版本
   */
  private createRegionShape(region: Region) {
    if (region.contour.length < 6) return; // 至少需要3个点

    const endTimer = this.performanceMonitor.startTimer('createRegionShape');

    try {
      // 智能颜色选择（基于置信度）
      const alpha = Math.max(0.6, region.confidence);
      const strokeColor = `rgba(237, 234, 22, ${alpha})`;

      // 创建封闭多边形
      const shape = new Konva.Line({
        points: region.contour,
        stroke: strokeColor,
        strokeWidth: Math.max(1, Math.min(3, region.confidence * 3)),
        strokeScaleEnabled: false,
        closed: true,
        tension: 0.1,
        lineJoin: 'round',
        lineCap: 'round',
        perfectDrawEnabled: false, // 性能优化
      });

      // 添加交互事件
      shape.on('click', () => {
        console.log(
          `区域 ${region.id}: ${region.pixels.size} 像素, 置信度: ${region.confidence.toFixed(2)}`
        );
      });

      // 存储到Map中
      this.regionShapes.set(region.id, shape);

      this.layer.add(shape);
      this.layer.draw();
    } catch (error) {
      console.error('创建区域形状时出错:', error);
    } finally {
      endTimer();
    }
  }

  /**
   * 创建标记并调用回调 - 高性能版本
   */
  private createMarkerAndCallback() {
    const endTimer = this.performanceMonitor.startTimer('createMarkerAndCallback');

    try {
      if (this.regionShapes.size > 0) {
        // 创建包含所有区域的组节点
        if (!this.node) {
          this.node = this.createNode();
          this.layer.add(this.node);
          this.setNodeId();
        }

        // 将所有区域形状添加到组中
        this.regionShapes.forEach((shape) => {
          this.node!.add(shape);
        });

        // 设置节点属性
        this.node.setAttrs({
          pixelCoords: {
            regionCount: this.regionShapes.size,
            regions: this.detectedRegions.map((region) => ({
              id: region.id,
              pixelCount: region.pixels.size,
              bounds: region.bounds,
              area: region.area,
              centroid: region.centroid,
              confidence: region.confidence,
            })),
          },
          Description: `智能魔棒选择 - ${this.regionShapes.size} 个区域`,
        });

        this.layer.draw();
      }
    } catch (error) {
      console.error('创建标记时出错:', error);
    } finally {
      endTimer();
    }
  }
}
